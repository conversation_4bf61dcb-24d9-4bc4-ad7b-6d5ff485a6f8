#!/usr/bin/env elixir

defmodule EctoToDropsMigrator do
  @moduledoc """
  Script to migrate the codebase from Ecto.Relation namespace to Drops.Relation
  and from :ecto_relation to :drops_relation.

  This script performs the following operations:
  1. Renames directories from lib/ecto/relation to lib/drops/relation
  2. Updates all module names from Ecto.Relation.* to Drops.Relation.*
  3. Updates all atom references from :ecto_relation to :drops_relation
  4. Updates configuration files
  5. Updates mix.exs and project configuration
  6. Updates test files
  7. Updates documentation and comments
  """

  def run do
    IO.puts("Starting migration from Ecto.Relation to Drops.Relation...")

    # Verify we're in the right directory
    unless File.exists?("mix.exs") and File.exists?("lib/ecto/relation") do
      IO.puts("Error: This script must be run from the ecto_relation project root directory.")
      System.halt(1)
    end

    # Create backup
    create_backup()

    # Step 1: Create new directory structure
    create_new_directory_structure()

    # Step 2: Move and update all files
    migrate_files()

    # Step 3: Update project configuration
    update_project_config()

    # Step 4: Update configuration files
    update_config_files()

    # Step 5: Update test files
    update_test_files()

    # Step 6: Update documentation
    update_documentation()

    # Step 7: Clean up old directories
    cleanup_old_directories()

    IO.puts("Migration completed successfully!")
    IO.puts("Please review the changes and run tests to ensure everything works correctly.")
    IO.puts("A backup was created in ./backup_before_migration/")
  end

  defp create_backup do
    IO.puts("Creating backup...")
    timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "")
    backup_dir = "backup_before_migration_#{timestamp}"

    # Create backup of critical files and directories
    File.mkdir_p!(backup_dir)

    # Backup lib directory
    if File.exists?("lib") do
      File.cp_r!("lib", Path.join(backup_dir, "lib"))
    end

    # Backup test directory
    if File.exists?("test") do
      File.cp_r!("test", Path.join(backup_dir, "test"))
    end

    # Backup config directory
    if File.exists?("config") do
      File.cp_r!("config", Path.join(backup_dir, "config"))
    end

    # Backup important files
    important_files = ["mix.exs", "README.md", ".formatter.exs"]
    Enum.each(important_files, fn file ->
      if File.exists?(file) do
        File.cp!(file, Path.join(backup_dir, file))
      end
    end)

    IO.puts("Backup created in #{backup_dir}")
  end

  defp create_new_directory_structure do
    IO.puts("Creating new directory structure...")
    File.mkdir_p!("lib/drops")
    File.mkdir_p!("lib/drops/relation")
    File.mkdir_p!("test/drops")
    File.mkdir_p!("test/drops/relation")
  end

  defp migrate_files do
    IO.puts("Migrating files...")

    # Get all .ex and .exs files in lib/ecto/relation
    lib_files = Path.wildcard("lib/ecto/relation/**/*.ex")

    Enum.each(lib_files, fn file_path ->
      migrate_lib_file(file_path)
    end)

    # Get all test files
    test_files = Path.wildcard("test/ecto/relation/**/*.exs") ++
                 Path.wildcard("test/ecto/relation_test.exs") ++
                 Path.wildcard("test/support/**/*.ex")

    Enum.each(test_files, fn file_path ->
      migrate_test_file(file_path)
    end)

    # Migrate mix tasks
    mix_task_files = Path.wildcard("lib/mix/tasks/**/*.ex")
    Enum.each(mix_task_files, fn file_path ->
      migrate_mix_task_file(file_path)
    end)

    # Update the drops.dev.setup.ex file
    migrate_drops_dev_setup()
  end

  defp migrate_lib_file(old_path) do
    new_path = String.replace(old_path, "lib/ecto/relation", "lib/drops/relation")

    # Ensure directory exists
    new_dir = Path.dirname(new_path)
    File.mkdir_p!(new_dir)

    # Read, transform, and write content
    content = File.read!(old_path)
    new_content = transform_content(content)
    File.write!(new_path, new_content)

    IO.puts("Migrated: #{old_path} -> #{new_path}")
  end

  defp migrate_test_file(old_path) do
    cond do
      String.contains?(old_path, "test/ecto/relation_test.exs") ->
        new_path = "test/drops/relation_test.exs"

      String.contains?(old_path, "test/ecto/relation/") ->
        new_path = String.replace(old_path, "test/ecto/relation", "test/drops/relation")

      String.contains?(old_path, "test/support/") ->
        new_path = old_path  # Keep support files in same location

      true ->
        new_path = old_path
    end

    # Ensure directory exists
    new_dir = Path.dirname(new_path)
    File.mkdir_p!(new_dir)

    # Read, transform, and write content
    content = File.read!(old_path)
    new_content = transform_content(content)
    File.write!(new_path, new_content)

    if old_path != new_path do
      IO.puts("Migrated: #{old_path} -> #{new_path}")
    else
      IO.puts("Updated: #{old_path}")
    end
  end

  defp migrate_mix_task_file(file_path) do
    content = File.read!(file_path)
    new_content = transform_content(content)
    File.write!(file_path, new_content)
    IO.puts("Updated: #{file_path}")
  end

  defp migrate_drops_dev_setup do
    file_path = "lib/mix/drops.dev.setup.ex"
    content = File.read!(file_path)
    new_content = transform_content(content)
    File.write!(file_path, new_content)
    IO.puts("Updated: #{file_path}")
  end

  defp transform_content(content) do
    content
    # Module name transformations (order matters - most specific first)
    |> String.replace("Ecto.Relation.Application", "Drops.Relation.Application")
    |> String.replace("Ecto.Relation.Supervisor", "Drops.Relation.Supervisor")
    |> String.replace("Ecto.Relation.MixProject", "Drops.Relation.MixProject")
    |> String.replace("Ecto.Relation.Repos", "Drops.Relation.Repos")
    |> String.replace("Ecto.Relation", "Drops.Relation")
    |> String.replace("defmodule Ecto.Relation", "defmodule Drops.Relation")
    |> String.replace("alias Ecto.Relation", "alias Drops.Relation")
    |> String.replace("import Ecto.Relation", "import Drops.Relation")
    |> String.replace("use Ecto.Relation", "use Drops.Relation")

    # Mix task transformations
    |> String.replace("Mix.Tasks.Ecto.Relation", "Mix.Tasks.Drops.Relation")
    |> String.replace("ecto.relation", "drops.relation")
    |> String.replace("group: :ecto_relation", "group: :drops_relation")

    # Atom transformations
    |> String.replace(":ecto_relation", ":drops_relation")

    # String transformations (be careful with order)
    |> String.replace("\"ecto_relation\"", "\"drops_relation\"")
    |> String.replace("name: \"ecto_relation\"", "name: \"drops_relation\"")
    |> String.replace("ecto_relation", "drops_relation")

    # Configuration transformations
    |> String.replace("config :ecto_relation", "config :drops_relation")
    |> String.replace("Application.get_env(:ecto_relation", "Application.get_env(:drops_relation")
    |> String.replace("Application.put_env(:ecto_relation", "Application.put_env(:drops_relation")
    |> String.replace("Application.ensure_all_started(:ecto_relation)", "Application.ensure_all_started(:drops_relation)")

    # File path transformations in strings
    |> String.replace("lib/ecto/relation", "lib/drops/relation")
    |> String.replace("test/ecto/relation", "test/drops/relation")

    # Log file transformations
    |> String.replace("ecto_relation_", "drops_relation_")
    |> String.replace(":ecto_relation}", ":drops_relation}")

    # Documentation and comment transformations
    |> String.replace("EctoRelation", "DropsRelation")
    |> String.replace("Ecto relation", "Drops relation")
    |> String.replace("ecto relation", "drops relation")
    |> String.replace("Ecto.Relation", "Drops.Relation")  # Catch any remaining

    # URL and GitHub references
    |> String.replace("github.com/solnic/ecto_relation", "github.com/solnic/drops_relation")
    |> String.replace("https://github.com/solnic/ecto_relation", "https://github.com/solnic/drops_relation")

    # Cache and other specific references
    |> String.replace("ecto_relation_sqlite_test.db", "drops_relation_sqlite_test.db")
    |> String.replace("ecto_relation_test.log", "drops_relation_test.log")

    # Dependency references in mix.exs files
    |> String.replace("{:ecto_relation,", "{:drops_relation,")

    # Environment variable references
    |> String.replace("ECTO_RELATION_", "DROPS_RELATION_")

    # Any remaining ecto_repos references that might point to our app
    |> String.replace("ecto_repos: [Ecto.Relation", "ecto_repos: [Drops.Relation")
  end

  defp update_project_config do
    IO.puts("Updating mix.exs...")

    content = File.read!("mix.exs")
    new_content = transform_content(content)

    # Additional specific transformations for mix.exs
    new_content = new_content
    |> String.replace("app: :ecto_relation", "app: :drops_relation")
    |> String.replace("@source_url \"https://github.com/solnic/ecto_relation\"",
                     "@source_url \"https://github.com/solnic/drops_relation\"")
    |> String.replace("files: ~w(lib/ecto/relation", "files: ~w(lib/drops/relation")

    File.write!("mix.exs", new_content)
  end

  defp update_config_files do
    IO.puts("Updating configuration files...")

    # Update config/config.exs
    if File.exists?("config/config.exs") do
      content = File.read!("config/config.exs")
      new_content = transform_content(content)
      File.write!("config/config.exs", new_content)
    end

    # Update config/runtime.exs
    if File.exists?("config/runtime.exs") do
      content = File.read!("config/runtime.exs")
      new_content = transform_content(content)
      File.write!("config/runtime.exs", new_content)
    end

    # Update test/test_helper.exs
    if File.exists?("test/test_helper.exs") do
      content = File.read!("test/test_helper.exs")
      new_content = transform_content(content)
      File.write!("test/test_helper.exs", new_content)
    end
  end

  defp update_test_files do
    IO.puts("Updating remaining test files...")

    # Update sample app files
    sample_app_files = Path.wildcard("test/sample_app/**/*.{ex,exs}")

    Enum.each(sample_app_files, fn file_path ->
      content = File.read!(file_path)
      new_content = transform_content(content)

      # Special handling for sample app mix.exs dependency
      new_content = if String.ends_with?(file_path, "mix.exs") do
        String.replace(new_content, "{:ecto_relation, path: \"../..\"}", "{:drops_relation, path: \"../..\"}")
      else
        new_content
      end

      File.write!(file_path, new_content)
    end)
  end

  defp update_documentation do
    IO.puts("Updating documentation...")

    # Update README.md
    if File.exists?("README.md") do
      content = File.read!("README.md")
      new_content = String.replace(content, "# Ecto.Relation", "# Drops.Relation")
      File.write!("README.md", new_content)
    end

    # Update any other documentation files
    doc_files = Path.wildcard("*.md") ++ Path.wildcard("docs/**/*.md")

    Enum.each(doc_files, fn file_path ->
      if File.exists?(file_path) do
        content = File.read!(file_path)
        new_content = transform_content(content)
        File.write!(file_path, new_content)
      end
    end)
  end

  defp cleanup_old_directories do
    IO.puts("Cleaning up old directories...")

    # Remove old lib/ecto directory if it's empty or only contains relation
    if File.exists?("lib/ecto/relation") do
      File.rm_rf!("lib/ecto/relation")

      # Check if lib/ecto is now empty and remove it
      case File.ls("lib/ecto") do
        {:ok, []} -> File.rmdir("lib/ecto")
        _ -> :ok
      end
    end

    # Remove old test/ecto directory
    if File.exists?("test/ecto") do
      File.rm_rf!("test/ecto")
    end

    # Rename database and log files
    rename_file_if_exists("priv/ecto_relation_sqlite_test.db", "priv/drops_relation_sqlite_test.db")
    rename_file_if_exists("log/ecto_relation_test.log", "log/drops_relation_test.log")
  end

  defp rename_file_if_exists(old_path, new_path) do
    if File.exists?(old_path) do
      File.rename(old_path, new_path)
      IO.puts("Renamed: #{old_path} -> #{new_path}")
    end
  end
end

# Check if script is being run directly
if System.argv() |> Enum.any?(&(&1 == "--help" or &1 == "-h")) do
  IO.puts("""
  Ecto.Relation to Drops.Relation Migration Script

  This script migrates the entire codebase from Ecto.Relation namespace to Drops.Relation
  and from :ecto_relation to :drops_relation.

  Usage:
    elixir migrate_to_drops.exs

  What it does:
  - Creates a backup of your current codebase
  - Renames all directories from lib/ecto/relation to lib/drops/relation
  - Updates all module names from Ecto.Relation.* to Drops.Relation.*
  - Updates all atom references from :ecto_relation to :drops_relation
  - Updates configuration files, mix.exs, and test files
  - Updates documentation and comments
  - Cleans up old directory structure

  Prerequisites:
  - Must be run from the project root directory
  - Requires lib/ecto/relation directory to exist
  - Requires mix.exs file to exist

  After running:
  - Review all changes carefully
  - Run tests to ensure everything works
  - Update any external references (CI, documentation, etc.)
  - Consider updating the GitHub repository name if applicable
  """)
  System.halt(0)
end

# Run the migration
IO.puts("To see usage instructions, run: elixir migrate_to_drops.exs --help")
IO.puts("")

# Confirm before proceeding
IO.puts("This script will migrate your codebase from Ecto.Relation to Drops.Relation.")
IO.puts("A backup will be created before making any changes.")
IO.write("Do you want to proceed? (y/N): ")

case IO.read(:line) |> String.trim() |> String.downcase() do
  "y" ->
    EctoToDropsMigrator.run()
  "yes" ->
    EctoToDropsMigrator.run()
  _ ->
    IO.puts("Migration cancelled.")
    System.halt(0)
end
